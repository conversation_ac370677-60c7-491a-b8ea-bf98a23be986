const axios = require('axios');

const BASE_URL = 'http://localhost:3002';

async function testAssessmentSessions() {
  try {
    console.log('🔍 Testing Assessment Sessions...\n');

    // Step 1: Login to get a fresh token
    console.log('Step 1: Getting authentication token...');
    const loginResponse = await axios.post(`${BASE_URL}/api/auth/login`, {
      username: 'admin',
      password: 'admin123'
    });

    if (!loginResponse.data.success) {
      console.log('❌ Login failed:', loginResponse.data.message);
      return;
    }

    const token = loginResponse.data.data.accessToken;
    console.log('✅ Login successful, token obtained');

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Step 2: Get list of patients to find valid patient IDs
    console.log('\nStep 2: Getting patient list...');
    const patientsResponse = await axios.get(`${BASE_URL}/api/patients`, { headers });
    
    if (!patientsResponse.data.success || !patientsResponse.data.data.length) {
      console.log('❌ No patients found');
      return;
    }

    const patients = patientsResponse.data.data;
    console.log(`✅ Found ${patients.length} patients`);
    
    // Show patient ID formats
    patients.forEach((patient, index) => {
      console.log(`Patient ${index + 1}:`);
      console.log(`  - UUID: ${patient.id}`);
      console.log(`  - Patient ID: ${patient.patientId}`);
      console.log(`  - Name: ${patient.firstName} ${patient.lastName}`);
    });

    const testPatient = patients[0];
    console.log(`\nUsing test patient: ${testPatient.firstName} ${testPatient.lastName}`);

    // Step 3: Test creating assessment session with UUID
    console.log('\nStep 3: Testing assessment session creation with UUID...');
    const assessmentData = {
      sessionDate: new Date().toISOString(),
      assessments: [
        {
          disorderId: 'major-depressive-disorder',
          criteria: [
            {
              criterionId: 'depressed-mood',
              met: true,
              notes: 'Patient reports persistent sadness'
            }
          ],
          severity: 'moderate',
          confidence: 'high',
          notes: 'Test assessment'
        }
      ],
      clinicalImpression: 'Test clinical impression',
      riskAssessment: {
        suicidalIdeation: false,
        homicidalIdeation: false,
        selfHarm: false,
        substanceUse: false,
        level: 'low'
      },
      status: 'completed',
      duration: 60
    };

    try {
      const createResponse = await axios.post(
        `${BASE_URL}/api/patients/${testPatient.id}/assessment-sessions`,
        assessmentData,
        { headers }
      );
      console.log('✅ Assessment session created with UUID:', createResponse.data);
    } catch (error) {
      console.log('❌ Failed to create assessment session with UUID:', error.response?.data || error.message);
    }

    // Step 4: Test creating assessment session with formatted patient ID
    console.log('\nStep 4: Testing assessment session creation with formatted patient ID...');
    try {
      const createResponse2 = await axios.post(
        `${BASE_URL}/api/patients/${testPatient.patientId}/assessment-sessions`,
        assessmentData,
        { headers }
      );
      console.log('✅ Assessment session created with formatted ID:', createResponse2.data);
    } catch (error) {
      console.log('❌ Failed to create assessment session with formatted ID:', error.response?.data || error.message);
    }

    // Step 5: Test getting assessment sessions
    console.log('\nStep 5: Testing getting assessment sessions...');
    try {
      const getResponse = await axios.get(
        `${BASE_URL}/api/patients/${testPatient.id}/assessment-sessions`,
        { headers }
      );
      console.log('✅ Retrieved assessment sessions:', getResponse.data);
    } catch (error) {
      console.log('❌ Failed to get assessment sessions:', error.response?.data || error.message);
    }

    console.log('\n🎉 Assessment session testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

testAssessmentSessions();
